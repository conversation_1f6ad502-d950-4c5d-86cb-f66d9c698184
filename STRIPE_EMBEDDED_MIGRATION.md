# Stripe Embedded Checkout Migration Guide

This guide explains how to migrate from hosted Stripe checkout to embedded checkout in your Next.js application.

## What Changed

### Server-Side Changes (`/api/checkout/route.ts`)

1. **Added `ui_mode: 'embedded'`** to the session creation
2. **Replaced `success_url` and `cancel_url`** with a single `return_url`
3. **Return `clientSecret`** instead of `url` in the response

### Client-Side Changes

1. **New Components Created:**
   - `EmbeddedCheckout.tsx` - Wrapper for <PERSON><PERSON>'s embedded checkout
   - `ConfirmPaymentEmbedded.tsx` - Updated payment confirmation with embedded checkout
   - `useEmbeddedCheckout.ts` - Custom hook for managing checkout state

2. **New Pages:**
   - `/payment-complete` - Handles return from embedded checkout
   - `/checkout-demo` - Demonstrates both hosted and embedded checkout

## Migration Steps

### Step 1: Update Your Checkout API Response Handling

**Before (Hosted):**
```javascript
const response = await fetch('/api/checkout', { /* ... */ });
const data = await response.json();

if (data.url) {
  window.location.href = data.url; // Redirect to Stripe
}
```

**After (Embedded):**
```javascript
const response = await fetch('/api/checkout', { /* ... */ });
const data = await response.json();

if (data.clientSecret) {
  setClientSecret(data.clientSecret); // Use in embedded component
}
```

### Step 2: Replace Redirect Logic with Embedded Component

**Before:**
```jsx
// Direct redirect to Stripe
window.location.href = checkoutUrl;
```

**After:**
```jsx
// Show embedded checkout
{clientSecret && <EmbeddedCheckout clientSecret={clientSecret} />}
```

### Step 3: Update Your Components

Replace your existing payment components with the new embedded versions:

```jsx
// Old
import ConfirmPayment from '@/components/basket/ConfirmPayment';

// New
import ConfirmPaymentEmbedded from '@/components/basket/ConfirmPaymentEmbedded';
```

## Key Benefits

1. **Better User Experience**: Users stay on your site throughout the payment process
2. **Mobile Optimized**: Better mobile experience with native scrolling and responsive design
3. **Customization**: Full control over the checkout flow and styling
4. **Reduced Abandonment**: No redirect means less chance of users dropping off

## Testing

1. **Visit `/checkout-demo`** to see both implementations side by side
2. **Test the embedded checkout** with your existing payment flows
3. **Verify the payment completion page** works correctly

## Environment Variables

Make sure you have the required Stripe environment variables:

```env
STRIPE_SECRET_KEY=sk_test_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
```

## Files Modified/Created

### Modified:
- `src/app/api/checkout/route.ts` - Updated to support embedded checkout
- `src/app/payment/page.tsx` - Added embedded checkout options

### Created:
- `src/components/EmbeddedCheckout.tsx` - Embedded checkout component
- `src/components/basket/ConfirmPaymentEmbedded.tsx` - Updated payment confirmation
- `src/hooks/useEmbeddedCheckout.ts` - Checkout state management hook
- `src/app/payment-complete/page.tsx` - Payment completion page
- `src/app/api/checkout-session/route.ts` - Session retrieval endpoint
- `src/app/checkout/page.tsx` - Standalone checkout page
- `src/app/checkout-demo/page.tsx` - Comparison demo

## Backward Compatibility

The updated `/api/checkout` endpoint is backward compatible. It will:
- Return `clientSecret` for embedded checkout (new)
- Still work with existing hosted checkout flows (if you modify the client-side handling)

## Next Steps

1. **Test thoroughly** with your existing payment flows
2. **Update your components** to use the embedded checkout
3. **Update your success/cancel page handling** to use the new return URL pattern
4. **Consider updating your mobile experience** to take advantage of the improved UX

## Troubleshooting

### Common Issues:

1. **"Loading checkout..." never resolves**: Check that `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` is set correctly
2. **Checkout form doesn't appear**: Verify the `clientSecret` is being passed correctly
3. **Payment completion not working**: Ensure the return URL is configured properly

### Debug Steps:

1. Check browser console for errors
2. Verify API responses include `clientSecret`
3. Test with Stripe's test card numbers
4. Check network tab for failed requests

## Support

If you encounter issues:
1. Check the Stripe documentation for embedded checkout
2. Verify your Stripe account settings
3. Test with the demo page first to isolate issues
