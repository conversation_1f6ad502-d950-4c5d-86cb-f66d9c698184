import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/lib/auth/serverAuth';

export async function POST(req: NextRequest) {
  try {
    // Get user ID from request
    const userId = await getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json(
        {
          error: 'Authentication required. Please log in to access Stripe Connect OAuth.',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      );
    }

    console.log('Creating Stripe Connect OAuth login for user:', userId);

    // Get the origin for redirect URLs
    const origin = req.headers.get('origin') || 'http://localhost:3000';
    
    const clientId = process.env.STRIPE_CONNECT_CLIENT_ID;
    
    if (!clientId) {
      return NextResponse.json(
        { error: 'Stripe Connect not configured. Please set STRIPE_CONNECT_CLIENT_ID in environment variables.' },
        { status: 500 }
      );
    }

    // Build OAuth URL for Stripe Connect
    const oauthParams = new URLSearchParams({
      response_type: 'code',
      client_id: clientId,
      scope: 'read_write',
      redirect_uri: `${origin}/api/connect/oauth-callback`,
      state: userId, // Pass user ID as state to identify user after OAuth
      'stripe_landing': 'login' // This tells Stripe to show login form instead of signup
    });

    const oauthUrl = `https://connect.stripe.com/oauth/authorize?${oauthParams.toString()}`;

    return NextResponse.json({
      success: true,
      message: 'OAuth login URL generated successfully',
      loginUrl: oauthUrl,
      userId,
      instructions: 'Open the loginUrl in a popup window or modal to authenticate with your existing Stripe account',
      popupFeatures: 'width=600,height=700,scrollbars=yes,resizable=yes,status=yes,location=yes'
    });

  } catch (error) {
    console.error('Error creating OAuth login:', error);
    return NextResponse.json(
      { error: 'Failed to create login URL' },
      { status: 500 }
    );
  }
}
