import { NextRequest, NextResponse } from 'next/server';
import { getOrderById } from '@/services/ordersServices';
import { getStripeInstance } from '@/lib/stripe';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      orderId, 
      chargeId, 
      paymentIntentId, 
      cancellationReason = 'requested_by_customer' 
    } = body;

    console.log('🚫 ===== ESCROW CANCEL REQUEST =====');
    console.log('📋 Order ID:', orderId);
    console.log('💳 Charge ID:', chargeId || 'Not provided');
    console.log('🎯 Payment Intent ID:', paymentIntentId || 'Not provided');
    console.log('📝 Cancellation Reason:', cancellationReason);
    console.log('🕐 Timestamp:', new Date().toISOString());

    if (!orderId) {
      return NextResponse.json({
        success: false,
        error: 'Order ID is required'
      }, { status: 400 });
    }

    // Get order details to find payment information
    console.log('🔍 Fetching order details...');
    const orderResult = await getOrderById(orderId);
    
    if (!orderResult.success || !orderResult.order) {
      console.error('❌ Order not found:', orderId);
      return NextResponse.json({
        success: false,
        error: 'Order not found'
      }, { status: 404 });
    }

    const order = orderResult.order;
    console.log('✅ Order found:', {
      id: order.id,
      status: order.status,
      chargeId: order.chargeId,
      transactionId: order.transactionId,
      payment_intent_id: (order as any).payment_intent_id
    });

    // Determine payment details to cancel
    // Priority: payment_intent_id (Stripe PI) > transactionId (Firebase transaction)
    const finalChargeId = chargeId || order.chargeId;
    const finalPaymentIntentId = paymentIntentId || (order as any).payment_intent_id || order.transactionId;

    if (!finalChargeId) {
      console.error('❌ No charge ID found');
      return NextResponse.json({
        success: false,
        error: 'Charge ID is required for escrow cancellation. Unable to resolve charge ID from the provided order.'
      }, { status: 400 });
    }

    console.log('💾 Using payment details:', {
      chargeId: finalChargeId,
      paymentIntentId: finalPaymentIntentId,
      cancellationReason,
      orderPaymentIntentId: (order as any).payment_intent_id,
      orderTransactionId: order.transactionId
    });

    // Determine which Stripe instance to use (assuming GBP for now, can be enhanced)
    const isUS = false; // You can enhance this based on order currency
    const stripe = getStripeInstance(isUS);

    let cancelResult = null;
    let cancelMethod = '';

    // Validate cancellation reason
    const validReasons = ['duplicate', 'fraudulent', 'requested_by_customer', 'abandoned'];
    const finalCancellationReason = validReasons.includes(cancellationReason) 
      ? cancellationReason 
      : 'requested_by_customer';

    if (cancellationReason !== finalCancellationReason) {
      console.log(`⚠️ Invalid cancellation reason '${cancellationReason}'. Using '${finalCancellationReason}' instead.`);
    }

    // Try to cancel payment intent first (preferred method)
    if (finalPaymentIntentId) {
      try {
        console.log('🔄 Attempting to cancel payment intent:', finalPaymentIntentId);
        
        // First, retrieve the payment intent to check its status
        const paymentIntent = await stripe.paymentIntents.retrieve(finalPaymentIntentId);
        console.log('📊 Payment Intent Status:', {
          id: paymentIntent.id,
          status: paymentIntent.status,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          capture_method: paymentIntent.capture_method
        });

        if (paymentIntent.status === 'requires_capture' || paymentIntent.status === 'requires_confirmation') {
          // Cancel the payment intent with reason
          cancelResult = await stripe.paymentIntents.cancel(finalPaymentIntentId, {
            cancellation_reason: finalCancellationReason as any
          });
          cancelMethod = 'payment_intent';
          console.log('✅ Payment intent cancelled successfully');
        } else if (paymentIntent.status === 'succeeded') {
          console.log('⚠️ Payment already succeeded, cannot cancel payment intent');
          
          // If payment succeeded, we might need to create a refund instead
          if (finalChargeId || paymentIntent.latest_charge) {
            const chargeToRefund = finalChargeId || paymentIntent.latest_charge as string;
            console.log('🔄 Creating refund for charge:', chargeToRefund);
            
            cancelResult = await stripe.refunds.create({
              charge: chargeToRefund,
              reason: finalCancellationReason as any
            });
            cancelMethod = 'refund';
            console.log('✅ Refund created successfully');
          }
        } else {
          console.log('ℹ️ Payment intent status does not allow cancellation:', paymentIntent.status);
        }
      } catch (error) {
        console.error('❌ Error with payment intent operation:', error);
        
        // If payment intent fails, try charge refund as fallback
        if (finalChargeId) {
          console.log('🔄 Falling back to charge refund...');
          try {
            cancelResult = await stripe.refunds.create({
              charge: finalChargeId,
              reason: finalCancellationReason as any
            });
            cancelMethod = 'refund_fallback';
            console.log('✅ Fallback refund created successfully');
          } catch (refundError) {
            console.error('❌ Fallback refund also failed:', refundError);
          }
        }
      }
    }

    // If no payment intent or it failed, try charge refund
    if (!cancelResult && finalChargeId) {
      try {
        console.log('🔄 Attempting to refund charge:', finalChargeId);
        cancelResult = await stripe.refunds.create({
          charge: finalChargeId,
          reason: finalCancellationReason as any
        });
        cancelMethod = 'charge_refund';
        console.log('✅ Charge refunded successfully');
      } catch (error) {
        console.error('❌ Error refunding charge:', error);
      }
    }

    if (!cancelResult) {
      return NextResponse.json({
        success: false,
        error: 'Failed to cancel or refund the payment. The payment may already be processed or in a non-cancellable state.'
      }, { status: 400 });
    }

    console.log('🎉 Escrow cancellation completed:', {
      method: cancelMethod,
      resultId: cancelResult.id,
      status: cancelResult.status,
      cancellationReason: finalCancellationReason
    });

    return NextResponse.json({
      success: true,
      message: `Escrow payment ${cancelMethod === 'payment_intent' ? 'cancelled' : 'refunded'} successfully`,
      data: {
        orderId,
        method: cancelMethod,
        cancellationReason: finalCancellationReason,
        [cancelMethod === 'payment_intent' ? 'paymentIntent' : 'refund']: {
          id: cancelResult.id,
          status: cancelResult.status,
          amount: cancelResult.amount
        }
      }
    });

  } catch (error) {
    console.error('❌ Escrow cancel error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json({
      success: false,
      error: `Failed to cancel escrow payment: ${errorMessage}`
    }, { status: 500 });
  }
}
