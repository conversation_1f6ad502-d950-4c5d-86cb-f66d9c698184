import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { getStripeInstanceByCurrency } from "@/lib/stripe";
import { getEscrowTransactionByOrderId } from "@/services/transactionService";

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

export async function POST(request: NextRequest) {
  try {
    const { orderId, stage, paymentIntentId } = await request.json();

    if (!orderId || !stage || !paymentIntentId) {
      return NextResponse.json(
        {
          success: false,
          error: "Order ID, stage, and payment intent ID are required",
        },
        { status: 400 }
      );
    }

    console.log("🚀 ===== ESCROW RELEASE BY PAYMENT INTENT =====");
    console.log(`📋 Order ID: ${orderId}`);
    console.log(`🎯 Stage: ${stage}`);
    console.log(`💳 Payment Intent ID: ${paymentIntentId}`);
    console.log("🕐 Timestamp:", new Date().toISOString());

    // Get escrow transaction to determine currency
    console.log("🔄 Step 0: Getting escrow transaction to determine currency...");
    const transactionResult = await getEscrowTransactionByOrderId(orderId);

    if (!transactionResult.success || !transactionResult.transaction) {
      return NextResponse.json(
        {
          success: false,
          error: "Escrow transaction not found for this order",
        },
        { status: 404 }
      );
    }

    const transaction = transactionResult.transaction;

    // Automatically select the correct Stripe instance based on transaction currency
    const { stripeInstance, isUS } = getStripeInstanceByCurrency(transaction.currency);
    console.log(
      `🌍 Using ${isUS ? "US" : "International"} Stripe instance for ${transaction.currency.toUpperCase()} currency`
    );

    // Step 1: Get payment intent and extract charge ID
    console.log("🔄 Step 1: Fetching payment intent and charges...");
    const paymentIntent = await stripeInstance.paymentIntents.retrieve(paymentIntentId, {
      expand: ["charges.data"],
    });

    console.log("✅ Payment Intent retrieved:", {
      id: paymentIntent.id,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      chargesCount: (paymentIntent as any).charges?.data?.length || 0,
    });

    // Find the latest charge
    const charges = (paymentIntent as any).charges?.data || [];
    if (charges.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: "No charges found for this payment intent",
        },
        { status: 400 }
      );
    }

    const latestCharge = charges[0]; // Most recent charge
    console.log("🎯 Latest charge found:", {
      id: latestCharge.id,
      status: latestCharge.status,
      amount: latestCharge.amount,
      captured: latestCharge.captured,
    });

    // Step 2: Calculate escrow stage amounts
    console.log("🔄 Step 2: Calculating escrow stage amounts...");
    const totalAmount = paymentIntent.amount;

    // Use the same calculation logic as transactionService.ts to ensure consistency
    const totalInMajorUnit = totalAmount / 100;
    const subtotalInMajorUnit = totalInMajorUnit / 1.04; // Remove 4% to get subtotal
    const platformCommissionInMajorUnit = subtotalInMajorUnit * 0.16; // 16% commission

    // Convert back to smallest currency unit for storage
    const subtotal = Math.round(subtotalInMajorUnit * 100);
    const platformCommission = Math.round(platformCommissionInMajorUnit * 100);
    const sellerAmount = subtotal - platformCommission; // Calculate as remainder to ensure exact total

    // Calculate escrow stages using the same logic as transactionService.ts
    const acceptAmount = Math.round(sellerAmount * 0.10); // 10%
    const deliveredAmount = Math.round(sellerAmount * 0.10); // 10%
    const completedAmount = sellerAmount - acceptAmount - deliveredAmount; // Remainder to ensure exact total

    let stageAmount = 0;
    let stagePercentage = 0;

    switch (stage) {
      case "accept":
        stageAmount = acceptAmount;
        stagePercentage = 10;
        break;
      case "delivered":
        stageAmount = deliveredAmount;
        stagePercentage = 10;
        break;
      case "completed":
        stageAmount = completedAmount;
        stagePercentage = 80;
        break;
      default:
        return NextResponse.json(
          {
            success: false,
            error: "Invalid stage. Must be accept, delivered, or completed",
          },
          { status: 400 }
        );
    }

    console.log("💰 Stage calculation:", {
      stage,
      percentage: stagePercentage,
      totalAmount,
      sellerAmount,
      stageAmount,
      stageAmountFormatted: `$${(stageAmount / 100).toFixed(2)}`,
    });

    // Step 3: Verify payment is captured (payment already deducted and held in Stripe)
    console.log("🔄 Step 3: Verifying payment is captured...");

    if (!latestCharge.captured) {
      console.error("❌ Charge not captured. Payment must be captured first.");
      return NextResponse.json(
        {
          error: "Payment must be captured first. This charge is only authorized.",
          details:
            "For escrow payments, the full payment should be captured and held in Stripe, then released in stages (10%, 10%, 80%) via transfers.",
          paymentIntentId,
          chargeId: latestCharge.id,
          chargeStatus: latestCharge.status,
          captured: latestCharge.captured,
        },
        { status: 400 }
      );
    }

    console.log("✅ Payment is captured and held in Stripe. Creating transfer for escrow stage...");

    // Step 4: Create transfer to seller (84% of captured amount)
    console.log("🔄 Step 4: Creating transfer to seller...");
    const transferAmount = Math.round(stageAmount * 0.84); // 84% of captured amount

    // Note: You'll need to get the seller's connected account ID
    // This is just an example - replace with actual seller account logic
    const sellerAccountId = "acct_seller_placeholder"; // Get from your database

    let transfer = null;
    try {
      transfer = await stripeInstance.transfers.create({
        amount: transferAmount,
        currency: paymentIntent.currency,
        destination: sellerAccountId,
        source_transaction: latestCharge.id,
        metadata: {
          orderId: orderId,
          escrowStage: stage,
          originalAmount: stageAmount.toString(),
          transferPercentage: "84",
        },
      });

      console.log("✅ Transfer created:", {
        transferId: transfer.id,
        amount: transfer.amount,
        destination: transfer.destination,
      });
    } catch (transferError) {
      console.warn("⚠️ Transfer creation failed:", transferError);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to create transfer",
          details:
            transferError instanceof Error ? transferError.message : "Unknown transfer error",
          paymentIntentId,
          chargeId: latestCharge.id,
        },
        { status: 400 }
      );
    }

    // Step 5: Prepare response
    const responseData = {
      success: true,
      orderId,
      stage,
      paymentIntentId,
      chargeId: latestCharge.id,
      escrowRelease: {
        stage,
        percentage: stagePercentage,
        amount: stageAmount,
        amountFormatted: `$${(stageAmount / 100).toFixed(2)}`,
        currency: paymentIntent.currency,
        description: `${stagePercentage}% of seller amount transferred from captured payment`,
      },
      paymentInfo: {
        totalCaptured: latestCharge.amount_captured,
        totalCapturedFormatted: `$${(latestCharge.amount_captured / 100).toFixed(2)}`,
        chargeStatus: latestCharge.status,
        captured: latestCharge.captured,
      },
      transfer: transfer
        ? {
            id: transfer.id,
            amount: transfer.amount,
            amountFormatted: `$${(transfer.amount / 100).toFixed(2)}`,
            destination: transfer.destination,
            description: `84% of escrow stage amount transferred to seller`,
          }
        : null,
      isUSStripeUsed: Boolean(isUS),
      timestamp: new Date().toISOString(),
    };

    console.log("✅ ===== ESCROW RELEASE COMPLETED =====");
    console.log(`🎯 Stage: ${stage} (${stagePercentage}%)`);
    console.log(`💰 Transfer Amount: $${(transferAmount / 100).toFixed(2)}`);
    console.log(`🏦 From Captured Payment: $${(latestCharge.amount_captured / 100).toFixed(2)}`);
    console.log(`🔗 Charge ID: ${latestCharge.id}`);
    console.log("🔚 ===== END ESCROW RELEASE =====");

    return NextResponse.json(responseData);
  } catch (error) {
    console.error("❌ Error in escrow release by payment intent:", error);

    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        {
          success: false,
          error: "Stripe API error",
          details: error.message,
          type: error.type,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: "Failed to release escrow stage",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
