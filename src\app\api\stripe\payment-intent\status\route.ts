import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const paymentIntentId = searchParams.get('payment_intent_id');

    if (!paymentIntentId) {
      return NextResponse.json({
        success: false,
        error: 'Payment Intent ID is required'
      }, { status: 400 });
    }

    console.log('🔍 Checking Payment Intent Status:', paymentIntentId);

    // Get payment intent details
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    console.log('📊 Payment Intent Details:', {
      id: paymentIntent.id,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      capture_method: paymentIntent.capture_method,
      latest_charge: paymentIntent.latest_charge
    });

    // Get charge details if available using latest_charge
    let chargeDetails = null;
    if (paymentIntent.latest_charge) {
      // Retrieve the latest charge separately to get full details
      const charge = await stripe.charges.retrieve(paymentIntent.latest_charge as string);
      chargeDetails = {
        id: charge.id,
        amount: charge.amount,
        captured: charge.captured,
        paid: charge.paid,
        status: charge.status
      };
    }

    // Determine next action based on status
    let nextAction = '';
    let readyForCapture = false;
    
    switch (paymentIntent.status) {
      case 'requires_payment_method':
        nextAction = 'User needs to complete payment via checkout URL';
        break;
      case 'requires_confirmation':
        nextAction = 'Payment method provided, needs confirmation';
        break;
      case 'requires_capture':
        nextAction = 'Payment authorized! Ready for capture and escrow release';
        readyForCapture = true;
        break;
      case 'succeeded':
        nextAction = 'Payment already captured and completed';
        break;
      default:
        nextAction = `Current status: ${paymentIntent.status}`;
    }

    return NextResponse.json({
      success: true,
      paymentIntentId: paymentIntent.id,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      captureMethod: paymentIntent.capture_method,
      readyForCapture,
      nextAction,
      chargeDetails,
      metadata: paymentIntent.metadata,
      timeline: {
        created: new Date(paymentIntent.created * 1000).toISOString(),
        lastUpdated: 'Real-time status check'
      }
    });

  } catch (error) {
    console.error('Error checking payment intent status:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to check payment intent status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
