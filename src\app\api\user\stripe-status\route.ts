import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/lib/auth/serverAuth';
import { checkUserStripeId } from '@/services/usersServices';

/**
 * GET /api/user/stripe-status
 * Check if the logged-in user has a Stripe ID
 * Returns basic status without fetching full Stripe account details
 */
export async function GET(req: NextRequest) {
  try {
    console.log('GET /api/user/stripe-status called');

    // Get user ID from request
    const userId = await getUserIdFromRequest(req);
    console.log('User ID from request:', userId);

    if (!userId) {
      console.log('No authenticated user found');
      return NextResponse.json(
        {
          error: 'Authentication required. Please log in to check your Stripe status.',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      );
    }

    // Check user's Stripe ID status
    const stripeStatus = await checkUserStripeId(userId);

    if (!stripeStatus.success) {
      return NextResponse.json(
        { 
          error: stripeStatus.error || 'Failed to check Stripe status',
          code: 'CHECK_FAILED'
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      userId,
      hasStripeId: stripeStatus.hasStripeId,
      stripeId: stripeStatus.stripeId,
      user: {
        id: stripeStatus.user?.id,
        profile_name: stripeStatus.user?.profile_name,
        email: stripeStatus.user?.email,
        stripe_id: stripeStatus.user?.stripe_id
      }
    });

  } catch (error) {
    console.error('Error checking Stripe status:', error);
    return NextResponse.json(
      { 
        error: 'Failed to check Stripe status',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}
