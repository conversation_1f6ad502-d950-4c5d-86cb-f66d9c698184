'use client';

import { useState } from 'react';
import EmbeddedCheckout from '@/components/EmbeddedCheckout';
import { useEmbeddedCheckout } from '@/hooks/useEmbeddedCheckout';

export default function CheckoutDemoPage() {
  const [activeTab, setActiveTab] = useState<'hosted' | 'embedded'>('embedded');
  const [hostedLoading, setHostedLoading] = useState(false);
  
  const { clientSecret, loading, error, createCheckoutSession, reset } = useEmbeddedCheckout();

  // Sample checkout data
  const sampleData = {
    userId: 'user_123',
    userEmail: '<EMAIL>',
    amount: 2000, // $20.00
    currency: 'usd',
    productName: 'Demo Product',
    productDescription: 'This is a demo product for testing checkout',
  };

  const sampleEscrowData = {
    ...sampleData,
    isEscrow: true,
    sellerId: 'seller_456',
    sellerEmail: '<EMAIL>',
    sellerStripeAccountId: 'acct_1234567890', // Replace with actual account ID
    orderId: 'order_789',
    productName: 'Escrow Service',
    productDescription: 'This is a demo escrow service',
  };

  // Hosted checkout (old method)
  const handleHostedCheckout = async (isEscrow = false) => {
    setHostedLoading(true);
    try {
      const data = isEscrow ? sampleEscrowData : sampleData;
      
      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result = await response.json();
      
      if (result.url) {
        // Old hosted method - redirect to Stripe
        window.location.href = result.url;
      } else if (result.clientSecret) {
        // This shouldn't happen with hosted, but just in case
        console.log('Got client secret instead of URL:', result.clientSecret);
      }
    } catch (error) {
      console.error('Error creating hosted checkout:', error);
    } finally {
      setHostedLoading(false);
    }
  };

  // Embedded checkout (new method)
  const handleEmbeddedCheckout = async (isEscrow = false) => {
    const data = isEscrow ? sampleEscrowData : sampleData;
    await createCheckoutSession(data);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Stripe Checkout Comparison
          </h1>
          <p className="text-gray-600">
            Compare hosted vs embedded Stripe checkout implementations
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="flex mb-8 border-b">
          <button
            onClick={() => setActiveTab('embedded')}
            className={`px-6 py-3 font-medium border-b-2 transition-colors ${
              activeTab === 'embedded'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            🎯 Embedded Checkout (New)
          </button>
          <button
            onClick={() => setActiveTab('hosted')}
            className={`px-6 py-3 font-medium border-b-2 transition-colors ${
              activeTab === 'hosted'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            🔗 Hosted Checkout (Old)
          </button>
        </div>

        {activeTab === 'embedded' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Controls */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Embedded Checkout</h2>
              <p className="text-gray-600 mb-6">
                The new embedded checkout keeps users on your site and provides a seamless experience.
              </p>

              <div className="space-y-4">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-medium mb-2">Regular Payment</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    ${(sampleData.amount / 100).toFixed(2)} - {sampleData.productName}
                  </p>
                  <button
                    onClick={() => handleEmbeddedCheckout(false)}
                    disabled={loading}
                    className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
                  >
                    {loading ? 'Creating Session...' : 'Create Regular Checkout'}
                  </button>
                </div>

                <div className="p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-medium mb-2">Escrow Payment</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    ${(sampleEscrowData.amount / 100).toFixed(2)} - {sampleEscrowData.productName}
                  </p>
                  <button
                    onClick={() => handleEmbeddedCheckout(true)}
                    disabled={loading}
                    className="w-full bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
                  >
                    {loading ? 'Creating Session...' : 'Create Escrow Checkout'}
                  </button>
                </div>

                {clientSecret && (
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <button
                      onClick={reset}
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      ← Reset Checkout
                    </button>
                  </div>
                )}

                {error && (
                  <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                    {error}
                  </div>
                )}
              </div>
            </div>

            {/* Embedded Checkout Form */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4">Checkout Form</h3>
              {clientSecret ? (
                <EmbeddedCheckout clientSecret={clientSecret} />
              ) : (
                <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
                  <p className="text-gray-500">Click a button to load the checkout form</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'hosted' && (
          <div className="bg-white rounded-lg shadow-md p-6 max-w-2xl mx-auto">
            <h2 className="text-xl font-semibold mb-4">Hosted Checkout</h2>
            <p className="text-gray-600 mb-6">
              The traditional hosted checkout redirects users to Stripe's website to complete payment.
            </p>

            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium mb-2">Regular Payment</h3>
                <p className="text-sm text-gray-600 mb-3">
                  ${(sampleData.amount / 100).toFixed(2)} - {sampleData.productName}
                </p>
                <button
                  onClick={() => handleHostedCheckout(false)}
                  disabled={hostedLoading}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
                >
                  {hostedLoading ? 'Redirecting...' : 'Redirect to Stripe (Regular)'}
                </button>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium mb-2">Escrow Payment</h3>
                <p className="text-sm text-gray-600 mb-3">
                  ${(sampleEscrowData.amount / 100).toFixed(2)} - {sampleEscrowData.productName}
                </p>
                <button
                  onClick={() => handleHostedCheckout(true)}
                  disabled={hostedLoading}
                  className="w-full bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
                >
                  {hostedLoading ? 'Redirecting...' : 'Redirect to Stripe (Escrow)'}
                </button>
              </div>
            </div>

            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h4 className="font-medium text-yellow-800 mb-2">Note:</h4>
              <p className="text-sm text-yellow-700">
                Hosted checkout will redirect you away from this page to Stripe's website. 
                You'll return to the payment completion page after finishing the payment.
              </p>
            </div>
          </div>
        )}

        {/* Comparison Table */}
        <div className="mt-12 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-6">Feature Comparison</h2>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">Feature</th>
                  <th className="text-left py-3 px-4">Hosted Checkout</th>
                  <th className="text-left py-3 px-4">Embedded Checkout</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b">
                  <td className="py-3 px-4 font-medium">User Experience</td>
                  <td className="py-3 px-4">Redirects to Stripe</td>
                  <td className="py-3 px-4">Stays on your site</td>
                </tr>
                <tr className="border-b">
                  <td className="py-3 px-4 font-medium">Customization</td>
                  <td className="py-3 px-4">Limited</td>
                  <td className="py-3 px-4">Full control</td>
                </tr>
                <tr className="border-b">
                  <td className="py-3 px-4 font-medium">Mobile Experience</td>
                  <td className="py-3 px-4">Good</td>
                  <td className="py-3 px-4">Excellent</td>
                </tr>
                <tr className="border-b">
                  <td className="py-3 px-4 font-medium">Implementation</td>
                  <td className="py-3 px-4">Simple redirect</td>
                  <td className="py-3 px-4">React components</td>
                </tr>
                <tr>
                  <td className="py-3 px-4 font-medium">Return URL</td>
                  <td className="py-3 px-4">success_url/cancel_url</td>
                  <td className="py-3 px-4">return_url</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
