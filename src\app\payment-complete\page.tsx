'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';

export default function PaymentCompletePage() {
  const [status, setStatus] = useState('loading');
  const [session, setSession] = useState<any>(null);
  const searchParams = useSearchParams();
  
  useEffect(() => {
    const sessionId = searchParams.get('session_id');
    const transactionId = searchParams.get('transaction_id');
    const orderId = searchParams.get('order_id');
    
    const fetchSession = async () => {
      if (!sessionId) {
        setStatus('error');
        return;
      }
      
      try {
        const response = await fetch(`/api/checkout-session?session_id=${sessionId}`);
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        
        const data = await response.json();
        setSession(data);
        setStatus(data.status);
        
        // If payment was successful, you can add additional logic here
        // such as updating order status, redirecting to specific pages, etc.
        if (data.status === 'complete') {
          console.log('Payment completed successfully!');
          console.log('Transaction ID:', transactionId);
          if (orderId) {
            console.log('Order ID:', orderId);
          }
        }
      } catch (error) {
        console.error('Error fetching session:', error);
        setStatus('error');
      }
    };
    
    fetchSession();
  }, [searchParams]);
  
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-lg">Processing your payment...</p>
        </div>
      </div>
    );
  }
  
  if (status === 'error') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-6xl mb-4">❌</div>
          <h1 className="text-2xl font-bold text-red-600 mb-2">Payment Error</h1>
          <p className="text-gray-600">Something went wrong with your payment. Please try again.</p>
          <button 
            onClick={() => window.history.back()}
            className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }
  
  if (status === 'open') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-yellow-600 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-yellow-600 mb-2">Payment Incomplete</h1>
          <p className="text-gray-600">Your payment wasn't completed. Please try again.</p>
          <button 
            onClick={() => window.history.back()}
            className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }
  
  // Payment successful
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center max-w-md mx-auto p-6">
        <div className="text-green-600 text-6xl mb-4">✅</div>
        <h1 className="text-3xl font-bold text-green-600 mb-4">Payment Successful!</h1>
        <p className="text-gray-600 mb-6">Thank you for your purchase. Your payment has been processed successfully.</p>
        
        {session && (
          <div className="bg-gray-50 p-4 rounded-lg mb-6 text-left">
            <h3 className="font-semibold mb-2">Payment Details:</h3>
            <p className="text-sm text-gray-600">Amount: ${(session.amount_total / 100).toFixed(2)} {session.currency?.toUpperCase()}</p>
            <p className="text-sm text-gray-600">Payment ID: {session.id}</p>
            {searchParams.get('transaction_id') && (
              <p className="text-sm text-gray-600">Transaction ID: {searchParams.get('transaction_id')}</p>
            )}
            {searchParams.get('order_id') && (
              <p className="text-sm text-gray-600">Order ID: {searchParams.get('order_id')}</p>
            )}
          </div>
        )}
        
        <div className="space-y-3">
          <button 
            onClick={() => window.location.href = '/'}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium"
          >
            Continue Shopping
          </button>
          <button 
            onClick={() => window.location.href = '/transactions'}
            className="w-full bg-gray-200 hover:bg-gray-300 text-gray-800 px-6 py-3 rounded-md font-medium"
          >
            View Transaction History
          </button>
        </div>
      </div>
    </div>
  );
}
