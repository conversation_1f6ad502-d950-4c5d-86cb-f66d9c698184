"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState, Suspense } from "react";
import { Transaction } from "@/services/transactionService";
import { AddtoOrderState, getOrderById } from "@/services/ordersServices";
import { getProfileNameByUserId } from "@/services/usersServices";
import { Modal, ModalBody, ModalContent, ModalHeader } from "@heroui/react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import Link from "next/link";

interface TransactionDetails {
  transaction?: Transaction;
  stripeSession?: {
    id: string;
    amount_total: number;
    currency: string;
    customer: any;
    payment_status: string;
    payment_intent: any;
    created: number;
    expires_at: number;
  };
  paymentIntent?: {
    id: string;
    status: string;
    amount: number;
    amount_capturable: number;
    amount_received: number;
    currency: string;
    latest_charge: string;
    charges: {
      data: Array<{
        id: string;
        status: string;
        amount: number;
        amount_captured: number;
        amount_refunded: number;
        currency: string;
        created: number;
        description: string;
        paid: boolean;
        refunded: boolean;
        captured: boolean;
        receipt_email: string;
        receipt_url: string;
        payment_method_details: any;
        billing_details: any;
        outcome: any;
        fraud_details: any;
        metadata: any;
      }>;
    };
    created: number;
    capture_method: string;
    receipt_email: string;
    customer: string;
    payment_method: string;
    confirmation_method: string;
    metadata: any;
  };
}

interface LatestChargeData {
  success: boolean;
  paymentIntent: any;
  latestCharge: {
    id: string;
    status: string;
    amount: number;
    amount_captured: number;
    amount_refunded: number;
    currency: string;
    created: number;
    description: string;
    paid: boolean;
    refunded: boolean;
    captured: boolean;
    receipt_email: string;
    receipt_url: string;
    payment_method_details: any;
    billing_details: any;
    outcome: any;
    fraud_details: any;
    metadata: any;
    payment_intent: string;
    payment_method: string;
  };
  allCharges: any[];
  chargeCount: number;
  timestamp: string;
}

function PaymentSuccessContent() {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get("session_id");
  const transactionId = searchParams.get("transaction_id");

  const [transactionDetails, setTransactionDetails] = useState<TransactionDetails | null>(null);
  const [latestChargeData, setLatestChargeData] = useState<LatestChargeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [chargeLoading, setChargeLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [orderStateUpdated, setOrderStateUpdated] = useState(false);
  const [showModal, setShowModal] = useState(true);
  const router = useRouter();

  // Helper function to check if payment is successful
  const isPaymentSuccessful = (data: any) => {
    // Check if payment intent status is succeeded
    if (data.paymentIntent?.status === "succeeded") {
      console.log("✅ Payment successful via payment intent status");
      return true;
    }

    // Check if stripe session payment status is paid
    if (data.stripeSession?.payment_status === "paid") {
      console.log("✅ Payment successful via stripe session status");
      return true;
    }

    // Check if latest charge is successful
    if (data.paymentIntent?.charges?.data?.[0]?.status === "succeeded") {
      console.log("✅ Payment successful via charge status");
      return true;
    }

    // Check if transaction status indicates completion
    if (data.transaction?.status === "completed") {
      console.log("✅ Payment successful via transaction status");
      return true;
    }

    // If we're on the payment-success page and have transaction data with orderId,
    // assume payment was successful (since Stripe redirected here)
    if (window.location.pathname.includes("payment-success") && data.transaction?.orderId) {
      console.log("✅ Payment assumed successful - on success page with valid transaction");
      return true;
    }

    console.log("❌ Payment not successful based on available data");
    return false;
  };

  // Function to store payment details in database
  const storePaymentDetails = async (transactionId: string) => {
    try {
      console.log("🔄 Step 3: Storing payment details in database...");
      console.log("📋 Transaction ID:", transactionId);

      const response = await fetch(`/api/transactions/${transactionId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      console.log("📡 Response status:", response.status);
      console.log("📡 Response ok:", response.ok);

      if (response.ok) {
        const result = await response.json();
        console.log("✅ Payment details stored successfully:", result);

        if (result.success) {
          if (result.data?.alreadyExists) {
            if (result.data?.skippedReason === "chargeId_exists") {
              console.log("🚫 chargeId already exists - NO database updates performed");
            } else {
              console.log("ℹ️ Payment details already exist in database, skipped update");
            }
          } else {
            console.log("💾 New payment data stored successfully:");
          }

          console.log("💾 Payment data:", {
            orderId: result.data?.orderId,
            chargeId: result.data?.chargeId,
            paymentIntentId: result.data?.paymentIntentId,
            paymentStatus: result.data?.paymentStatus,
            alreadyExists: result.data?.alreadyExists || false,
            skippedReason: result.data?.skippedReason || "none",
          });
          return result;
        } else {
          console.error("❌ Failed to store payment details:", result.error);
        }
      } else {
        const errorText = await response.text();
        console.error("❌ HTTP error storing payment details:", response.status, errorText);
      }
    } catch (error) {
      console.error("❌ Error storing payment details:", error);
    }
    return null;
  };

  // Function to update order state after successful payment
  const updateOrderState = async (transaction: Transaction) => {
    if (orderStateUpdated || !transaction.orderId) {
      return; // Already updated or no order ID
    }

    try {
      console.log("🔄 Updating order state for successful payment...");

      // Step 1: Check current order status from Firebase
      console.log("🔍 Checking current order status...");
      const orderResult = await getOrderById(transaction.orderId);

      if (!orderResult.success || !orderResult.order) {
        console.error("❌ Order not found:", orderResult.error);
        return;
      }

      const currentStatus = orderResult.order.status;
      console.log("📋 Current order status:", currentStatus);

      // Step 2: Only proceed if order status is BASKET
      if (currentStatus !== "BASKET") {
        console.log("🚫 Order status is not BASKET, skipping AddtoOrderState");
        console.log("ℹ️ Current status:", currentStatus, "- AddtoOrderState only runs for BASKET status");
        setOrderStateUpdated(true); // Mark as updated to prevent retries
        return;
      }

      console.log("✅ Order status is BASKET, proceeding with AddtoOrderState...");

      // Step 3: Get profile names for buyer and seller
      const [buyerProfileName, sellerProfileName] = await Promise.all([
        getProfileNameByUserId(transaction.userId),
        transaction.sellerId ? getProfileNameByUserId(transaction.sellerId) : null,
      ]);

      if (!buyerProfileName) {
        console.warn("⚠️ Could not fetch buyer profile name");
        return;
      }

      if (!sellerProfileName && transaction.sellerId) {
        console.warn("⚠️ Could not fetch seller profile name");
        return;
      }

      // Step 4: Call AddtoOrderState only if status is BASKET
      await AddtoOrderState({
        id: transaction.orderId,
        loggedInUser: buyerProfileName,
        sellerName: sellerProfileName || "Unknown Seller",
        userName: buyerProfileName,
      });

      setOrderStateUpdated(true);
      console.log("✅ Order state updated successfully from BASKET to NEW");
    } catch (error) {
      console.error("❌ Error updating order state:", error);
    }
  };

  // Function to fetch latest charge data
  const fetchLatestChargeData = async (paymentIntentId: string) => {
    setChargeLoading(true);
    try {
      console.log("🔄 Step 2: Fetching latest charge data for payment intent:", paymentIntentId);

      const response = await fetch(`/api/charges/latest?payment_intent_id=${paymentIntentId}`);

      if (response.ok) {
        const chargeData = await response.json();
        console.log("🎯 Latest Charge Data Response:", chargeData);

        if (chargeData.success) {
          setLatestChargeData(chargeData);
          console.log("✅ Charge ID Found:", chargeData.latestCharge.id);
        } else {
          console.warn("⚠️ No charge data found:", chargeData.error);
        }
      } else {
        console.error("❌ Failed to fetch charge data:", response.status);
      }
    } catch (error) {
      console.error("❌ Error fetching charge data:", error);
    } finally {
      setChargeLoading(false);
    }
  };

  useEffect(() => {
    const fetchTransactionDetails = async () => {
      if (!sessionId && !transactionId) {
        setError("No transaction information found");
        setLoading(false);
        return;
      }

      try {
        let response;
        let data;

        console.log("🔍 ===== PAYMENT SUCCESS PAGE LOADED =====");
        console.log("🎫 Session ID:", sessionId);
        console.log("📋 Transaction ID:", transactionId);
        console.log("🕐 Timestamp:", new Date().toISOString());

        // Immediately try to store payment details if we have transaction ID
        if (transactionId) {
          console.log(
            "🔄 Immediately attempting to store payment details for transaction:",
            transactionId
          );
          await storePaymentDetails(transactionId);
        }

        // Try session ID first
        if (sessionId) {
          console.log("Fetching transaction by session ID:", sessionId);
          response = await fetch(`/api/transactions/session/${sessionId}`);

          if (response?.ok) {
            data = await response.json();
            if (data.success) {
              setTransactionDetails(data);

              // Step 3: Update order state if payment is successful and we have transaction data
              console.log("🔍 Checking conditions for order state update:");
              console.log("- Has transaction:", !!data.transaction);
              console.log("- Payment successful:", isPaymentSuccessful(data));
              console.log("- Transaction data:", data.transaction);

              if (data.transaction && isPaymentSuccessful(data)) {
                console.log("✅ Conditions met, calling updateOrderState");

                // Step 3a: Store payment details in database first
                console.log(
                  "🔄 About to call storePaymentDetails with transaction ID:",
                  data.transaction.id
                );
                await storePaymentDetails(data.transaction.id);

                // Step 3b: Update order state
                await updateOrderState(data.transaction);
              } else {
                console.log("❌ Conditions not met for order state update");
                console.log("- Has transaction:", !!data.transaction);
                console.log("- Payment successful:", isPaymentSuccessful(data));
                console.log("- Transaction ID from URL:", transactionId);

                // Try to store payment details anyway if we have transaction ID from URL
                if (transactionId && data.transaction) {
                  console.log(
                    "🔄 Trying to store payment details with URL transaction ID:",
                    transactionId
                  );
                  await storePaymentDetails(transactionId);
                }
              }

              // Step 2: Fetch latest charge data if we have payment intent
              if (data.paymentIntent?.id) {
                await fetchLatestChargeData(data.paymentIntent.id);
              }
              return;
            }
          }

          // If session lookup failed but we have transaction ID, try direct lookup
          if (transactionId) {
            console.log("Session lookup failed, trying direct transaction lookup:", transactionId);
            response = await fetch(`/api/transactions/${transactionId}`);
          }
        } else if (transactionId) {
          // Direct transaction lookup
          console.log("Fetching transaction by ID:", transactionId);
          response = await fetch(`/api/transactions/${transactionId}`);
        }

        if (response?.ok) {
          data = await response.json();
          console.log("🔍 Step 1 Complete - Transaction Details:", data);

          if (data.success) {
            setTransactionDetails(data);

            // Step 3: Update order state if payment is successful and we have transaction data
            console.log("🔍 Checking conditions for order state update (second path):");
            console.log("- Has transaction:", !!data.transaction);
            console.log("- Payment successful:", isPaymentSuccessful(data));
            console.log("- Transaction data:", data.transaction);

            if (data.transaction && isPaymentSuccessful(data)) {
              console.log("✅ Conditions met, calling updateOrderState");

              // Step 3a: Store payment details in database first
              console.log(
                "🔄 About to call storePaymentDetails with transaction ID:",
                data.transaction.id
              );
              await storePaymentDetails(data.transaction.id);

              // Step 3b: Update order state
              await updateOrderState(data.transaction);
            } else {
              console.log("❌ Conditions not met for order state update");
              console.log("- Has transaction:", !!data.transaction);
              console.log("- Payment successful:", isPaymentSuccessful(data));
              console.log("- Transaction ID from URL:", transactionId);

              // Try to store payment details anyway if we have transaction ID from URL
              if (transactionId && data.transaction) {
                console.log(
                  "🔄 Trying to store payment details with URL transaction ID:",
                  transactionId
                );
                await storePaymentDetails(transactionId);
              }
            }

            // Step 2: Fetch latest charge data if we have payment intent
            if (data.paymentIntent?.id) {
              await fetchLatestChargeData(data.paymentIntent.id);
            } else {
              console.warn("⚠️ No payment intent ID found, skipping charge data fetch");
            }
          } else {
            setError(data.error || "Failed to fetch transaction details");
          }
        } else {
          setError("Failed to fetch transaction details");
        }
      } catch (err) {
        console.error("Error fetching transaction details:", err);
        setError("Failed to fetch transaction details");
      } finally {
        setLoading(false);
      }
    };

    fetchTransactionDetails();
  }, [sessionId, transactionId]);

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const formatDate = (date: Date | string | number) => {
    const dateObj = typeof date === "number" ? new Date(date * 1000) : new Date(date);
    return dateObj.toLocaleString();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-green-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading transaction details...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        backdrop="blur"
        isDismissable={false}
        hideCloseButton={true}
      >
        <ModalContent className="rounded-3xl">
          <ModalBody className="rounded-3xl">
            <div className="text-center p-3 ">
              <div className="flex justify-center items-center mb-4">
                <img src="/assets/check.svg" alt="" />
              </div>
              <p className="mb-6">
                The amount has been pre-authorized Your order will be confirmed by the service
                provider within 48 hour.
              </p>

              <div
                className="rounded-full w-full mt-5 cursor-pointer  border-2 py-5 text-base btn btn-sm "
                onClick={() => {
                  router.push(`${window.location.pathname}?sidebar=orders`, {
                    scroll: false,
                  });
                  window.dispatchEvent(new Event("openSidebar"));
                }}
              >
                Go to Order
              </div>
              <div
                className="rounded-full w-full mt-3 cursor-pointer  border-2 py-5 text-base btn btn-sm "
                onClick={() => {
                  router.push(`/`);
                }}
              >
                Home
              </div>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
      <div className="min-h-screen flex items-center justify-center bg-green-50 p-4">
        <div className="max-w-2xl w-full bg-white rounded-lg shadow-md p-8">
          <div className="text-center mb-6">
            <div className="mb-4">
              <svg
                className="mx-auto h-16 w-16 text-green-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Payment Successful!</h1>
            <p className="text-gray-600">
              Thank you for your payment. Your transaction has been completed successfully.
            </p>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
              <p className="text-red-600">{error}</p>
            </div>
          )}

          {orderStateUpdated && (
            <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
              <div className="flex items-center">
                <svg
                  className="w-5 h-5 text-green-600 mr-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <p className="text-green-800 font-medium">
                  ✅ Order placed successfully! Your order status has been updated.
                </p>
              </div>
            </div>
          )}

          {transactionDetails && (
            <div className="space-y-6">
              {/* PRIMARY: Stripe Charge Information */}
              {(latestChargeData?.latestCharge ||
                transactionDetails.paymentIntent?.charges?.data?.[0] ||
                transactionDetails.paymentIntent?.latest_charge) && (
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6 border-2 border-green-300 shadow-lg">
                  <h2 className="text-3xl font-bold text-green-800 mb-6 flex items-center">
                    <svg
                      className="w-8 h-8 mr-3 text-green-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    Payment Receipt (Stripe Charge)
                    {chargeLoading && (
                      <div className="ml-3 animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
                    )}
                  </h2>

                  {chargeLoading && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
                        <p className="text-blue-800 font-medium">
                          🔄 Fetching latest charge data from Stripe...
                        </p>
                      </div>
                    </div>
                  )}

                  {latestChargeData && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                      <div className="flex items-center">
                        <svg
                          className="w-5 h-5 text-green-600 mr-3"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        <p className="text-green-800 font-medium">
                          ✅ Latest charge data loaded ({latestChargeData.chargeCount} charges
                          found) - Updated:{" "}
                          {new Date(latestChargeData.timestamp).toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  )}

                  {(() => {
                    // Priority: Use latest charge data from API, then fallback to transaction data
                    let displayCharge;

                    if (latestChargeData?.latestCharge) {
                      // Use fresh charge data from Step 2 API call
                      displayCharge = latestChargeData.latestCharge;
                      console.log("🎯 Using latest charge data from API:", displayCharge.id);
                    } else if (transactionDetails.paymentIntent?.charges?.data?.[0]) {
                      // Use charge data from transaction API
                      displayCharge = transactionDetails.paymentIntent.charges.data[0];
                      console.log("🎯 Using charge data from transaction:", displayCharge.id);
                    } else if (transactionDetails.paymentIntent?.latest_charge) {
                      // Create minimal charge object from payment intent data
                      const pi = transactionDetails.paymentIntent;
                      displayCharge = {
                        id: pi.latest_charge,
                        status: pi.status === "succeeded" ? "succeeded" : "pending",
                        amount: pi.amount,
                        amount_captured: pi.amount_received || 0,
                        amount_refunded: 0,
                        currency: pi.currency,
                        created: pi.created,
                        paid: pi.status === "succeeded",
                        captured: (pi.amount_received || 0) > 0,
                        refunded: false,
                        receipt_email: pi.receipt_email || "",
                        receipt_url: null,
                        payment_method_details: null,
                        billing_details: null,
                        outcome: null,
                        fraud_details: null,
                        description: null,
                        metadata: pi.metadata || {},
                      };
                      console.log(
                        "🎯 Using minimal charge data from payment intent:",
                        displayCharge.id
                      );
                    } else {
                      console.error("❌ No charge data available");
                      return <div>No charge data available</div>;
                    }

                    return (
                      <div className="space-y-6">
                        {/* Primary Charge Information */}
                        <div className="bg-white p-6 rounded-lg border-2 border-green-200 shadow-sm">
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {/* Charge ID - Most Important */}
                            <div className="md:col-span-2 lg:col-span-1">
                              <label className="block text-sm font-bold text-green-700 mb-2">
                                🆔 CHARGE ID
                              </label>
                              <div className="bg-green-50 p-4 rounded-lg border-2 border-green-300">
                                <p className="text-xl font-mono font-bold text-green-900 break-all">
                                  {displayCharge.id}
                                </p>
                                <button
                                  onClick={(e) => {
                                    navigator.clipboard.writeText(displayCharge.id);
                                    const btn = e.target as HTMLButtonElement;
                                    const originalText = btn.textContent;
                                    btn.textContent = "✓ Copied!";
                                    setTimeout(() => {
                                      btn.textContent = originalText;
                                    }, 2000);
                                  }}
                                  className="mt-2 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                                >
                                  📋 Copy ID
                                </button>
                              </div>
                            </div>

                            {/* Payment Status - Primary */}
                            <div>
                              <label className="block text-sm font-bold text-green-700 mb-2">
                                💳 PAYMENT STATUS
                              </label>
                              <div className="bg-white p-4 rounded-lg border">
                                <span
                                  className={`inline-flex items-center px-4 py-2 rounded-full text-lg font-bold ${
                                    displayCharge.status === "succeeded"
                                      ? "bg-green-100 text-green-800"
                                      : displayCharge.status === "pending"
                                        ? "bg-yellow-100 text-yellow-800"
                                        : "bg-red-100 text-red-800"
                                  }`}
                                >
                                  {displayCharge.status.toUpperCase()}
                                </span>
                                <div className="mt-2 text-sm text-gray-600">
                                  <p>✅ Paid: {displayCharge.paid ? "Yes" : "No"}</p>
                                  <p>📦 Captured: {displayCharge.captured ? "Yes" : "No"}</p>
                                  <p>↩️ Refunded: {displayCharge.refunded ? "Yes" : "No"}</p>
                                </div>
                              </div>
                            </div>

                            {/* Amount Information */}
                            <div>
                              <label className="block text-sm font-bold text-green-700 mb-2">
                                💰 AMOUNT DETAILS
                              </label>
                              <div className="bg-white p-4 rounded-lg border space-y-2">
                                <div>
                                  <span className="text-sm text-gray-600">Total:</span>
                                  <p className="text-2xl font-bold text-gray-900">
                                    {formatAmount(displayCharge.amount, displayCharge.currency)}
                                  </p>
                                </div>
                                <div>
                                  <span className="text-sm text-gray-600">Captured:</span>
                                  <p className="text-lg font-semibold text-green-600">
                                    {formatAmount(
                                      displayCharge.amount_captured,
                                      displayCharge.currency
                                    )}
                                  </p>
                                </div>
                                {displayCharge.amount_refunded > 0 && (
                                  <div>
                                    <span className="text-sm text-gray-600">Refunded:</span>
                                    <p className="text-lg font-semibold text-red-600">
                                      {formatAmount(
                                        displayCharge.amount_refunded,
                                        displayCharge.currency
                                      )}
                                    </p>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Detailed Charge Information */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          {/* Transaction Details */}
                          <div className="bg-white p-6 rounded-lg border shadow-sm">
                            <h3 className="text-lg font-bold text-gray-900 mb-4">
                              📋 Transaction Details
                            </h3>
                            <div className="space-y-3">
                              <div>
                                <label className="block text-sm font-medium text-gray-700">
                                  Created
                                </label>
                                <p className="text-sm text-gray-900">
                                  {new Date(displayCharge.created * 1000).toLocaleString()}
                                </p>
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700">
                                  Currency
                                </label>
                                <p className="text-sm text-gray-900 uppercase font-bold">
                                  {displayCharge.currency}
                                </p>
                              </div>
                              {displayCharge.description && (
                                <div>
                                  <label className="block text-sm font-medium text-gray-700">
                                    Description
                                  </label>
                                  <p className="text-sm text-gray-900">
                                    {displayCharge.description}
                                  </p>
                                </div>
                              )}
                              {displayCharge.receipt_email && (
                                <div>
                                  <label className="block text-sm font-medium text-gray-700">
                                    Receipt Email
                                  </label>
                                  <p className="text-sm text-gray-900">
                                    {displayCharge.receipt_email}
                                  </p>
                                </div>
                              )}
                              {displayCharge.receipt_url && (
                                <div>
                                  <label className="block text-sm font-medium text-gray-700">
                                    Receipt URL
                                  </label>
                                  <a
                                    href={displayCharge.receipt_url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-sm text-blue-600 hover:text-blue-800 underline"
                                  >
                                    View Receipt
                                  </a>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Payment Method Details */}
                          {displayCharge.payment_method_details && (
                            <div className="bg-white p-6 rounded-lg border shadow-sm">
                              <h3 className="text-lg font-bold text-gray-900 mb-4">
                                💳 Payment Method
                              </h3>
                              <div className="space-y-3">
                                {displayCharge.payment_method_details.card && (
                                  <>
                                    <div>
                                      <label className="block text-sm font-medium text-gray-700">
                                        Card Brand
                                      </label>
                                      <p className="text-sm text-gray-900 capitalize">
                                        {displayCharge.payment_method_details.card.brand}
                                      </p>
                                    </div>
                                    <div>
                                      <label className="block text-sm font-medium text-gray-700">
                                        Last 4 Digits
                                      </label>
                                      <p className="text-sm text-gray-900">
                                        **** **** ****{" "}
                                        {displayCharge.payment_method_details.card.last4}
                                      </p>
                                    </div>
                                    <div>
                                      <label className="block text-sm font-medium text-gray-700">
                                        Exp Date
                                      </label>
                                      <p className="text-sm text-gray-900">
                                        {displayCharge.payment_method_details.card.exp_month}/
                                        {displayCharge.payment_method_details.card.exp_year}
                                      </p>
                                    </div>
                                    {displayCharge.payment_method_details.card.country && (
                                      <div>
                                        <label className="block text-sm font-medium text-gray-700">
                                          Card Country
                                        </label>
                                        <p className="text-sm text-gray-900">
                                          {displayCharge.payment_method_details.card.country}
                                        </p>
                                      </div>
                                    )}
                                  </>
                                )}
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Billing Details */}
                        {displayCharge.billing_details && (
                          <div className="bg-white p-6 rounded-lg border shadow-sm">
                            <h3 className="text-lg font-bold text-gray-900 mb-4">
                              📍 Billing Information
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {displayCharge.billing_details.name && (
                                <div>
                                  <label className="block text-sm font-medium text-gray-700">
                                    Name
                                  </label>
                                  <p className="text-sm text-gray-900">
                                    {displayCharge.billing_details.name}
                                  </p>
                                </div>
                              )}
                              {displayCharge.billing_details.email && (
                                <div>
                                  <label className="block text-sm font-medium text-gray-700">
                                    Email
                                  </label>
                                  <p className="text-sm text-gray-900">
                                    {displayCharge.billing_details.email}
                                  </p>
                                </div>
                              )}
                              {displayCharge.billing_details.phone && (
                                <div>
                                  <label className="block text-sm font-medium text-gray-700">
                                    Phone
                                  </label>
                                  <p className="text-sm text-gray-900">
                                    {displayCharge.billing_details.phone}
                                  </p>
                                </div>
                              )}
                              {displayCharge.billing_details.address && (
                                <div className="md:col-span-2">
                                  <label className="block text-sm font-medium text-gray-700">
                                    Address
                                  </label>
                                  <div className="text-sm text-gray-900">
                                    {displayCharge.billing_details.address.line1 && (
                                      <p>{displayCharge.billing_details.address.line1}</p>
                                    )}
                                    {displayCharge.billing_details.address.line2 && (
                                      <p>{displayCharge.billing_details.address.line2}</p>
                                    )}
                                    <p>
                                      {displayCharge.billing_details.address.city &&
                                        `${displayCharge.billing_details.address.city}, `}
                                      {displayCharge.billing_details.address.state &&
                                        `${displayCharge.billing_details.address.state} `}
                                      {displayCharge.billing_details.address.postal_code}
                                    </p>
                                    {displayCharge.billing_details.address.country && (
                                      <p>{displayCharge.billing_details.address.country}</p>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        {/* Risk & Security Information */}
                        {(displayCharge.outcome || displayCharge.fraud_details) && (
                          <div className="bg-white p-6 rounded-lg border shadow-sm">
                            <h3 className="text-lg font-bold text-gray-900 mb-4">
                              🔒 Security & Risk Assessment
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {displayCharge.outcome && (
                                <div>
                                  <label className="block text-sm font-medium text-gray-700">
                                    Transaction Outcome
                                  </label>
                                  <div className="text-sm text-gray-900">
                                    <p>
                                      <strong>Type:</strong> {displayCharge.outcome.type}
                                    </p>
                                    <p>
                                      <strong>Network Status:</strong>{" "}
                                      {displayCharge.outcome.network_status}
                                    </p>
                                    {displayCharge.outcome.reason && (
                                      <p>
                                        <strong>Reason:</strong> {displayCharge.outcome.reason}
                                      </p>
                                    )}
                                    {displayCharge.outcome.risk_level && (
                                      <p>
                                        <strong>Risk Level:</strong>{" "}
                                        {displayCharge.outcome.risk_level}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              )}
                              {displayCharge.fraud_details &&
                                Object.keys(displayCharge.fraud_details).length > 0 && (
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700">
                                      Fraud Details
                                    </label>
                                    <div className="text-sm text-gray-900">
                                      {Object.entries(displayCharge.fraud_details).map(
                                        ([key, value]) => (
                                          <p key={key}>
                                            <strong>{key}:</strong> {String(value)}
                                          </p>
                                        )
                                      )}
                                    </div>
                                  </div>
                                )}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </div>
              )}

              {/* SECONDARY: Payment Intent Summary */}
              {transactionDetails.paymentIntent && (
                <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                  <h2 className="text-xl font-semibold text-blue-800 mb-4 flex items-center">
                    <svg
                      className="w-6 h-6 mr-2 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                      />
                    </svg>
                    Payment Intent Details
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="bg-white p-4 rounded-lg border">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Payment Intent ID
                      </label>
                      <p className="text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all">
                        {transactionDetails.paymentIntent.id}
                      </p>
                    </div>
                    <div className="bg-white p-4 rounded-lg border">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Intent Status
                      </label>
                      <span
                        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          transactionDetails.paymentIntent.status === "succeeded"
                            ? "bg-green-100 text-green-800"
                            : transactionDetails.paymentIntent.status === "requires_capture"
                              ? "bg-orange-100 text-orange-800"
                              : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {transactionDetails.paymentIntent.status.replace("_", " ").toUpperCase()}
                      </span>
                    </div>
                    <div className="bg-white p-4 rounded-lg border">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Capture Method
                      </label>
                      <span
                        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          transactionDetails.paymentIntent.capture_method === "manual"
                            ? "bg-orange-100 text-orange-800"
                            : "bg-blue-100 text-blue-800"
                        }`}
                      >
                        {transactionDetails.paymentIntent.capture_method.toUpperCase()}
                      </span>
                    </div>
                    {transactionDetails.paymentIntent.customer && (
                      <div className="bg-white p-4 rounded-lg border">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Customer ID
                        </label>
                        <p className="text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded">
                          {transactionDetails.paymentIntent.customer}
                        </p>
                      </div>
                    )}
                    {transactionDetails.paymentIntent.payment_method && (
                      <div className="bg-white p-4 rounded-lg border">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Payment Method ID
                        </label>
                        <p className="text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded">
                          {transactionDetails.paymentIntent.payment_method}
                        </p>
                      </div>
                    )}
                    <div className="bg-white p-4 rounded-lg border">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Created
                      </label>
                      <p className="text-sm text-gray-900">
                        {new Date(transactionDetails.paymentIntent.created * 1000).toLocaleString()}
                      </p>
                    </div>
                  </div>

                  {/* Escrow Information */}
                  {transactionDetails.paymentIntent.amount_capturable > 0 && (
                    <div className="mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                      <h3 className="text-sm font-bold text-orange-800 mb-2">
                        🔒 Escrow Information
                      </h3>
                      <p className="text-sm text-orange-700">
                        <strong>Amount Available for Capture:</strong>{" "}
                        {formatAmount(
                          transactionDetails.paymentIntent.amount_capturable,
                          transactionDetails.paymentIntent.currency
                        )}
                      </p>
                      <p className="text-xs text-orange-600 mt-1">
                        This amount is authorized but not yet captured. Use the charge ID above in
                        escrow APIs to capture funds.
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Stripe Session Details (if available) */}
              {transactionDetails.stripeSession && (
                <div className="bg-blue-50 rounded-lg p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    Checkout Session Details
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Session ID</label>
                      <p className="text-sm text-gray-900 font-mono bg-white px-3 py-2 rounded border">
                        {transactionDetails.stripeSession.id}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Payment Status
                      </label>
                      <span
                        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          transactionDetails.stripeSession.payment_status === "paid"
                            ? "bg-green-100 text-green-800"
                            : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {transactionDetails.stripeSession.payment_status}
                      </span>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Session Created
                      </label>
                      <p className="text-sm text-gray-900">
                        {new Date(transactionDetails.stripeSession.created * 1000).toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Session Expires
                      </label>
                      <p className="text-sm text-gray-900">
                        {new Date(
                          transactionDetails.stripeSession.expires_at * 1000
                        ).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Firebase Transaction Record (Secondary Info) */}
              {transactionDetails.transaction && (
                <div className="bg-gray-50 rounded-lg p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    Transaction Record (Firebase)
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Transaction ID
                      </label>
                      <p className="text-sm text-gray-900 font-mono">
                        {transactionDetails.transaction.id}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Order ID</label>
                      <p className="text-sm text-gray-900">
                        {transactionDetails.transaction.orderId}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Product</label>
                      <p className="text-sm text-gray-900">
                        {transactionDetails.transaction.productName}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Customer Email
                      </label>
                      <p className="text-sm text-gray-900">
                        {transactionDetails.transaction.userEmail}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Receipt Information */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Receipt Information</h2>
                <div className="text-sm text-gray-600 space-y-2">
                  <p>• A confirmation email has been sent to your registered email address.</p>
                  <p>
                    • Please keep this transaction ID for your records:{" "}
                    <span className="font-mono text-gray-900">
                      {transactionDetails.transaction?.id}
                    </span>
                  </p>
                  <p>• If you have any questions, please contact our support team.</p>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => (window.location.href = "/")}
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-md transition-colors"
            >
              Return to Home
            </button>
            <button
              onClick={() => (window.location.href = "/payment")}
              className="bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-6 rounded-md transition-colors"
            >
              Make Another Payment
            </button>
            {transactionDetails?.transaction && (
              <button
                onClick={() => {
                  const receiptData = {
                    transactionId: transactionDetails.transaction?.id,
                    amount: transactionDetails.transaction?.amount,
                    currency: transactionDetails.transaction?.currency,
                    productName: transactionDetails.transaction?.productName,
                    date: transactionDetails.transaction?.createdAt,
                    email: transactionDetails.transaction?.userEmail,
                  };

                  const dataStr = JSON.stringify(receiptData, null, 2);
                  const dataBlob = new Blob([dataStr], { type: "application/json" });
                  const url = URL.createObjectURL(dataBlob);
                  const link = document.createElement("a");
                  link.href = url;
                  link.download = `receipt-${transactionDetails.transaction?.id}.json`;
                  link.click();
                  URL.revokeObjectURL(url);
                }}
                className="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-md transition-colors"
              >
                Download Receipt
              </button>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

export default function PaymentSuccess() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-green-50">
          <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-green-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      }
    >
      <PaymentSuccessContent />
    </Suspense>
  );
}
