import { useState, useCallback } from 'react';

interface CheckoutData {
  userId: string;
  userEmail: string;
  amount: number;
  currency?: string;
  productName: string;
  productDescription?: string;
  isEscrow?: boolean;
  sellerId?: string;
  sellerEmail?: string;
  sellerStripeAccountId?: string;
  orderId?: string;
}

interface CheckoutResponse {
  clientSecret: string;
  sessionId: string;
  customerId: string;
  transactionId: string;
  isEscrow?: boolean;
  orderId?: string;
  sellerId?: string;
}

export const useEmbeddedCheckout = () => {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [checkoutData, setCheckoutData] = useState<CheckoutResponse | null>(null);

  const createCheckoutSession = useCallback(async (data: CheckoutData) => {
    setLoading(true);
    setError(null);
    setClientSecret(null);
    setCheckoutData(null);

    try {
      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: CheckoutResponse = await response.json();

      if (!result.clientSecret) {
        throw new Error('No client secret received from server');
      }

      setClientSecret(result.clientSecret);
      setCheckoutData(result);
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error creating checkout session:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setClientSecret(null);
    setError(null);
    setCheckoutData(null);
    setLoading(false);
  }, []);

  return {
    clientSecret,
    loading,
    error,
    checkoutData,
    createCheckoutSession,
    reset,
  };
};
